using System;
using System.Collections.Generic;
using System.Linq;

namespace EventManager
{
    /// <summary>
    /// Manages all events in the conference center
    /// </summary>
    public class EventManager
    {
        private List<Event> events;

        public EventManager()
        {
            events = new List<Event>();
        }

        // Property to get all events (read-only)
        public IReadOnlyList<Event> Events => events.AsReadOnly();

        // Add a workshop with validation
        public void AddWorkshop(string eventName, int eventID, int capacity, string topic, string company)
        {
            ValidateEventID(eventID);
            ValidateEventName(eventName);
            
            Workshop workshop = new Workshop(eventName, eventID, capacity, topic, company);
            events.Add(workshop);
        }

        // Add a workshop with default capacity (method overloading)
        public void AddWorkshop(string eventName, int eventID, string topic, string company)
        {
            ValidateEventID(eventID);
            ValidateEventName(eventName);
            
            Workshop workshop = new Workshop(eventName, eventID, topic, company);
            events.Add(workshop);
        }

        // Add a seminar with validation
        public void AddSeminar(string eventName, int eventID, int capacity, string speaker)
        {
            ValidateEventID(eventID);
            ValidateEventName(eventName);
            
            Seminar seminar = new Seminar(eventName, eventID, capacity, speaker);
            events.Add(seminar);
        }

        // Add a seminar with default capacity (method overloading)
        public void AddSeminar(string eventName, int eventID, string speaker)
        {
            ValidateEventID(eventID);
            ValidateEventName(eventName);
            
            Seminar seminar = new Seminar(eventName, eventID, speaker);
            events.Add(seminar);
        }

        // Get all events as formatted string
        public string GetAllEventsInfo()
        {
            if (events.Count == 0)
                return "No events scheduled.";

            string result = $"Total Events: {events.Count}\n\n";
            for (int i = 0; i < events.Count; i++)
            {
                result += $"Event {i + 1}:\n";
                result += events[i].GetEventDetails();
                result += "\n" + new string('-', 40) + "\n";
            }
            return result;
        }

        // Get events by type
        public List<T> GetEventsByType<T>() where T : Event
        {
            return events.OfType<T>().ToList();
        }

        // Find event by ID
        public Event FindEventById(int eventID)
        {
            return events.FirstOrDefault(e => e.EventID == eventID);
        }

        // Remove event by ID
        public bool RemoveEvent(int eventID)
        {
            Event eventToRemove = FindEventById(eventID);
            if (eventToRemove != null)
            {
                events.Remove(eventToRemove);
                return true;
            }
            return false;
        }

        // Get event count
        public int GetEventCount()
        {
            return events.Count;
        }

        // Get workshop count
        public int GetWorkshopCount()
        {
            return events.OfType<Workshop>().Count();
        }

        // Get seminar count
        public int GetSeminarCount()
        {
            return events.OfType<Seminar>().Count();
        }

        // Private validation methods
        private void ValidateEventID(int eventID)
        {
            if (eventID <= 0)
                throw new ArgumentException("Event ID must be a positive number.");
            
            if (events.Any(e => e.EventID == eventID))
                throw new DuplicateEventIdException($"Event with ID {eventID} already exists.");
        }

        private void ValidateEventName(string eventName)
        {
            if (string.IsNullOrWhiteSpace(eventName))
                throw new ArgumentException("Event name cannot be empty or null.");
        }

        // Clear all events
        public void ClearAllEvents()
        {
            events.Clear();
        }
    }
}
