using System;

namespace EventManager
{
    /// <summary>
    /// Base class for all events in the conference center
    /// </summary>
    public abstract class Event
    {
        protected string eventName;
        protected int eventID;
        protected int capacity;

        // Constructor with all parameters
        public Event(string eventName, int eventID, int capacity)
        {
            if (capacity < 0)
                throw new InvalidCapacityException("Capacity cannot be negative.");
            
            this.eventName = eventName;
            this.eventID = eventID;
            this.capacity = capacity;
        }

        // Constructor with default capacity (method overloading)
        public Event(string eventName, int eventID) : this(eventName, eventID, 50)
        {
        }

        // Properties
        public string EventName
        {
            get { return eventName; }
            set { eventName = value; }
        }

        public int EventID
        {
            get { return eventID; }
            set { eventID = value; }
        }

        public int Capacity
        {
            get { return capacity; }
            set 
            { 
                if (value < 0)
                    throw new InvalidCapacityException("Capacity cannot be negative.");
                capacity = value; 
            }
        }

        // Method overloading for displaying event information
        public virtual string DisplayInfo()
        {
            return $"Event ID: {eventID}\nEvent Name: {eventName}\nCapacity: {capacity}";
        }

        public virtual string DisplayInfo(bool includeType)
        {
            string info = DisplayInfo();
            if (includeType)
                info = $"Type: {this.GetType().Name}\n{info}";
            return info;
        }

        // Abstract method to be implemented by derived classes
        public abstract string GetEventDetails();

        // Virtual method that can be overridden
        public virtual string GetEventType()
        {
            return "General Event";
        }
    }
}
