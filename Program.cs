using System;
using System.Windows.Forms;

namespace EventManager
{
    /// <summary>
    /// Main program entry point for the Conference Center Event Management System
    /// </summary>
    internal static class Program
    {
        /// <summary>
        /// The main entry point for the application.
        /// </summary>
        [STAThread]
        static void Main()
        {
            try
            {
                // Enable visual styles for modern appearance
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                // Set up global exception handling
                Application.SetUnhandledExceptionMode(UnhandledExceptionMode.CatchException);
                Application.ThreadException += Application_ThreadException;
                AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;

                // Check if running in console mode for testing
                string[] args = Environment.GetCommandLineArgs();
                if (args.Length > 1 && args[1] == "--test")
                {
                    // Run console tests
                    TestProgram.RunTests();
                    Console.WriteLine("\nPress any key to continue to GUI...");
                    Console.ReadKey();
                }

                // Create and run the main form
                using (var mainForm = new MainForm())
                {
                    Application.Run(mainForm);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"A critical error occurred while starting the application:\n\n{ex.Message}",
                    "Critical Error",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles unhandled exceptions in the UI thread
        /// </summary>
        private static void Application_ThreadException(object sender, System.Threading.ThreadExceptionEventArgs e)
        {
            HandleUnhandledException(e.Exception, "UI Thread Exception");
        }

        /// <summary>
        /// Handles unhandled exceptions in non-UI threads
        /// </summary>
        private static void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            if (e.ExceptionObject is Exception ex)
            {
                HandleUnhandledException(ex, "Application Domain Exception");
            }
        }

        /// <summary>
        /// Common exception handling logic
        /// </summary>
        private static void HandleUnhandledException(Exception ex, string context)
        {
            try
            {
                string errorMessage = $"{context}:\n\n{ex.Message}";

                if (ex.InnerException != null)
                {
                    errorMessage += $"\n\nInner Exception: {ex.InnerException.Message}";
                }

                MessageBox.Show(
                    errorMessage,
                    "Unhandled Exception",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
            catch
            {
                // If we can't even show a message box, try to write to console
                Console.WriteLine($"Critical error in exception handler: {ex}");
            }
        }
    }
}
