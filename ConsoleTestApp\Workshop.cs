using System;

namespace EventManager
{
    /// <summary>
    /// Workshop class derived from Event
    /// </summary>
    public class Workshop : Event
    {
        private string topic;
        private string company;

        // Constructor with all parameters
        public Workshop(string eventName, int eventID, int capacity, string topic, string company)
            : base(eventName, eventID, capacity)
        {
            this.topic = topic ?? throw new ArgumentNullException(nameof(topic));
            this.company = company ?? throw new ArgumentNullException(nameof(company));
        }

        // Constructor with default capacity (method overloading)
        public Workshop(string eventName, int eventID, string topic, string company)
            : base(eventName, eventID)
        {
            this.topic = topic ?? throw new ArgumentNullException(nameof(topic));
            this.company = company ?? throw new ArgumentNullException(nameof(company));
        }

        // Properties
        public string Topic
        {
            get { return topic; }
            set { topic = value ?? throw new ArgumentNullException(nameof(value)); }
        }

        public string Company
        {
            get { return company; }
            set { company = value ?? throw new ArgumentNullException(nameof(value)); }
        }

        // Override abstract method
        public override string GetEventDetails()
        {
            return $"{DisplayInfo()}\nTopic: {topic}\nCompany: {company}";
        }

        // Override virtual method
        public override string GetEventType()
        {
            return "Workshop";
        }

        // Method overloading for displaying workshop-specific information
        public string DisplayWorkshopInfo()
        {
            return $"Workshop: {eventName}\nTopic: {topic}\nCompany: {company}";
        }

        public string DisplayWorkshopInfo(bool includeCapacity)
        {
            string info = DisplayWorkshopInfo();
            if (includeCapacity)
                info += $"\nCapacity: {capacity}";
            return info;
        }

        // Override ToString for easy display
        public override string ToString()
        {
            return $"Workshop - {eventName} (ID: {eventID})";
        }
    }
}
