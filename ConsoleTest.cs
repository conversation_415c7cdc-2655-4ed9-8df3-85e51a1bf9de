using System;

namespace EventManager
{
    /// <summary>
    /// Console application to test OOP principles without GUI
    /// </summary>
    class ConsoleTest
    {
        static void Main(string[] args)
        {
            Console.WriteLine("=== Conference Center Event Management System - Console Test ===\n");

            try
            {
                // Test 1: Create EventManager
                Console.WriteLine("Test 1: Creating Event Manager...");
                EventManager eventManager = new EventManager();
                Console.WriteLine("✓ Event Manager created successfully\n");

                // Test 2: Method Overloading - Workshop with default capacity
                Console.WriteLine("Test 2: Adding Workshop with default capacity (Method Overloading)...");
                eventManager.AddWorkshop("C# Programming Basics", 1, "Object-Oriented Programming", "TechCorp");
                Console.WriteLine("✓ Workshop added with default capacity (50)\n");

                // Test 3: Method Overloading - Workshop with custom capacity
                Console.WriteLine("Test 3: Adding Workshop with custom capacity (Method Overloading)...");
                eventManager.AddWorkshop("Advanced C# Concepts", 2, 75, "Design Patterns", "DevCompany");
                Console.WriteLine("✓ Workshop added with custom capacity (75)\n");

                // Test 4: Method Overloading - Seminar with default capacity
                Console.WriteLine("Test 4: Adding Seminar with default capacity (Method Overloading)...");
                eventManager.AddSeminar("Future of AI", 3, "Dr. Jane Smith");
                Console.WriteLine("✓ Seminar added with default capacity (50)\n");

                // Test 5: Method Overloading - Seminar with custom capacity
                Console.WriteLine("Test 5: Adding Seminar with custom capacity (Method Overloading)...");
                eventManager.AddSeminar("Machine Learning Trends", 4, 100, "Prof. John Doe");
                Console.WriteLine("✓ Seminar added with custom capacity (100)\n");

                // Test 6: Polymorphism - Display all events
                Console.WriteLine("Test 6: Displaying all events (Polymorphism in action)...");
                Console.WriteLine(eventManager.GetAllEventsInfo());

                // Test 7: Exception Handling - Invalid capacity
                Console.WriteLine("Test 7: Testing Exception Handling - Invalid Capacity...");
                try
                {
                    eventManager.AddWorkshop("Invalid Workshop", 5, -10, "Test Topic", "Test Company");
                    Console.WriteLine("❌ Should have thrown an exception!");
                }
                catch (InvalidCapacityException ex)
                {
                    Console.WriteLine($"✓ Caught InvalidCapacityException: {ex.Message}\n");
                }

                // Test 8: Exception Handling - Duplicate Event ID
                Console.WriteLine("Test 8: Testing Exception Handling - Duplicate Event ID...");
                try
                {
                    eventManager.AddSeminar("Duplicate ID Seminar", 1, "Another Speaker"); // ID 1 already exists
                    Console.WriteLine("❌ Should have thrown an exception!");
                }
                catch (DuplicateEventIdException ex)
                {
                    Console.WriteLine($"✓ Caught DuplicateEventIdException: {ex.Message}\n");
                }

                // Test 9: Inheritance and Polymorphism - Type checking
                Console.WriteLine("Test 9: Testing Inheritance and Polymorphism...");
                var events = eventManager.Events;
                foreach (var evt in events)
                {
                    Console.WriteLine($"Event Type: {evt.GetEventType()}");
                    Console.WriteLine($"Details: {evt.GetEventDetails()}");
                    Console.WriteLine($"ToString: {evt.ToString()}");
                    Console.WriteLine(new string('-', 40));
                }

                // Test 10: Statistics
                Console.WriteLine("Test 10: Event Statistics...");
                Console.WriteLine($"Total Events: {eventManager.GetEventCount()}");
                Console.WriteLine($"Workshops: {eventManager.GetWorkshopCount()}");
                Console.WriteLine($"Seminars: {eventManager.GetSeminarCount()}\n");

                // Test 11: Switch Statement Example - Event Type Processing
                Console.WriteLine("Test 11: Switch Statement Example - Processing Events by Type...");
                ProcessEventsByType(eventManager);

                Console.WriteLine("=== All Tests Completed Successfully! ===");
                Console.WriteLine("\nPress any key to exit...");
                Console.ReadKey();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Test failed with exception: {ex.Message}");
                Console.WriteLine($"Stack Trace: {ex.StackTrace}");
                Console.WriteLine("\nPress any key to exit...");
                Console.ReadKey();
            }
        }

        /// <summary>
        /// Demonstrates switch statement usage for processing different event types
        /// </summary>
        private static void ProcessEventsByType(EventManager eventManager)
        {
            foreach (var evt in eventManager.Events)
            {
                string processResult = evt switch
                {
                    Workshop workshop => $"Processing Workshop: {workshop.EventName} - Topic: {workshop.Topic}, Company: {workshop.Company}",
                    Seminar seminar => $"Processing Seminar: {seminar.EventName} - Speaker: {seminar.Speaker}",
                    _ => $"Processing Unknown Event Type: {evt.EventName}"
                };

                Console.WriteLine(processResult);
            }
            Console.WriteLine();
        }
    }
}
