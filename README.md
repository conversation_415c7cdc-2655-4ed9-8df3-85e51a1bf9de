# Conference Center Event Management System

A comprehensive C# Windows Forms application for managing different types of events in a conference center, demonstrating Object-Oriented Programming (OOP) principles.

## Features

### OOP Principles Implemented

1. **Inheritance**
   - Base class `Event` with common attributes (eventName, eventID, capacity)
   - Derived classes `Workshop` and `Seminar` with unique attributes
   - Abstract methods and virtual method overriding

2. **Polymorphism**
   - Virtual methods that can be overridden in derived classes
   - Abstract methods that must be implemented by derived classes
   - Runtime polymorphism through base class references

3. **Method Overloading**
   - Multiple constructors with different parameter sets
   - Overloaded display methods for different output formats
   - Overloaded Add methods in EventManager class

4. **Exception Handling**
   - Custom exception classes for specific error scenarios
   - Try-catch blocks with proper error messages
   - Input validation with user-friendly error reporting

5. **Switch Statements**
   - Menu-driven interface implementation
   - Exception type handling using switch expressions
   - Event type categorization

### Data Structures

- **List<Event>**: Stores all events with type safety and dynamic sizing
- **Generic collections**: Type-safe storage and retrieval
- **LINQ operations**: Efficient querying and filtering

### GUI Features

- **Windows Forms Interface**: Modern, user-friendly design
- **Menu System**: File, Event, View, and Help menus
- **Dialog Forms**: Separate forms for adding workshops and seminars
- **Input Validation**: Real-time validation with error messages
- **Exception Display**: User-friendly error reporting

## Class Structure

### Base Classes

- **Event**: Abstract base class with common properties and methods
- **EventManager**: Manages collection of events with CRUD operations

### Derived Classes

- **Workshop**: Inherits from Event, adds Topic and Company properties
- **Seminar**: Inherits from Event, adds Speaker property

### Exception Classes

- **InvalidCapacityException**: For negative capacity values
- **DuplicateEventIdException**: For duplicate event IDs
- **InvalidMenuChoiceException**: For invalid menu selections

### Form Classes

- **MainForm**: Main application window with menu and controls
- **AddWorkshopDialog**: Dialog for adding workshop events
- **AddSeminarDialog**: Dialog for adding seminar events

## Usage

### Menu Options

1. **Add Workshop**
   - Enter event name, ID, capacity, topic, and company
   - Option to use default capacity (50)
   - Input validation and error handling

2. **Add Seminar**
   - Enter event name, ID, capacity, and speaker
   - Option to use default capacity (50)
   - Input validation and error handling

3. **View All Events**
   - Display all scheduled events with details
   - Shows event type, properties, and capacity

4. **Statistics**
   - View total events, workshops, and seminars count
   - Quick overview of event distribution

5. **Exit**
   - Safe application termination with confirmation

### Error Handling

The application handles various error scenarios:

- **Invalid Capacity**: Negative values are rejected
- **Duplicate Event IDs**: Prevents duplicate event registration
- **Empty Fields**: Validates required input fields
- **Format Errors**: Handles invalid number formats
- **Null References**: Prevents null value assignments

## Technical Requirements

- **.NET 6.0 or later**
- **Windows Forms support**
- **Visual Studio 2022** (recommended) or any C# IDE

## Building and Running

1. **Using Visual Studio:**
   ```
   Open EventManager.csproj
   Build Solution (Ctrl+Shift+B)
   Run (F5)
   ```

2. **Using Command Line:**
   ```bash
   dotnet build
   dotnet run
   ```

## Code Examples

### Method Overloading Example
```csharp
// Constructor overloading in Event class
public Event(string eventName, int eventID, int capacity)
public Event(string eventName, int eventID) // Uses default capacity

// Method overloading in EventManager
public void AddWorkshop(string name, int id, int capacity, string topic, string company)
public void AddWorkshop(string name, int id, string topic, string company) // Default capacity
```

### Polymorphism Example
```csharp
// Base class reference, derived class object
Event workshop = new Workshop("C# Programming", 1, "Programming", "TechCorp");
Event seminar = new Seminar("AI Future", 2, "Dr. Smith");

// Polymorphic method calls
string workshopDetails = workshop.GetEventDetails(); // Calls Workshop implementation
string seminarDetails = seminar.GetEventDetails();   // Calls Seminar implementation
```

### Exception Handling Example
```csharp
try
{
    eventManager.AddWorkshop(name, id, capacity, topic, company);
}
catch (InvalidCapacityException ex)
{
    MessageBox.Show($"Invalid capacity: {ex.Message}");
}
catch (DuplicateEventIdException ex)
{
    MessageBox.Show($"Duplicate ID: {ex.Message}");
}
```

## Learning Objectives Achieved

✅ **Inheritance**: Base and derived classes with proper relationships  
✅ **Polymorphism**: Virtual methods and runtime type resolution  
✅ **Method Overloading**: Multiple method signatures for flexibility  
✅ **Exception Handling**: Custom exceptions and proper error management  
✅ **Switch Statements**: Menu handling and type-based processing  
✅ **Data Structures**: Generic collections for type-safe storage  
✅ **GUI Programming**: Windows Forms with event-driven architecture  
✅ **Input Validation**: User input validation and error prevention  

## Future Enhancements

- **Database Integration**: Persistent storage using Entity Framework
- **Event Scheduling**: Calendar integration and time management
- **Reporting**: Export events to PDF or Excel formats
- **Search and Filter**: Advanced event search capabilities
- **User Management**: Multi-user support with authentication
- **Event Categories**: Additional event types and categorization
