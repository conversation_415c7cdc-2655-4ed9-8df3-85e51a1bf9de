using System;

namespace EventManager
{
    /// <summary>
    /// Custom exception for invalid capacity values
    /// </summary>
    public class InvalidCapacityException : Exception
    {
        public InvalidCapacityException() : base("Invalid capacity value.")
        {
        }

        public InvalidCapacityException(string message) : base(message)
        {
        }

        public InvalidCapacityException(string message, Exception innerException) 
            : base(message, innerException)
        {
        }
    }

    /// <summary>
    /// Custom exception for invalid menu choices
    /// </summary>
    public class InvalidMenuChoiceException : Exception
    {
        public InvalidMenuChoiceException() : base("Invalid menu choice.")
        {
        }

        public InvalidMenuChoiceException(string message) : base(message)
        {
        }

        public InvalidMenuChoiceException(string message, Exception innerException) 
            : base(message, innerException)
        {
        }
    }

    /// <summary>
    /// Custom exception for duplicate event IDs
    /// </summary>
    public class DuplicateEventIdException : Exception
    {
        public DuplicateEventIdException() : base("Event ID already exists.")
        {
        }

        public DuplicateEventIdException(string message) : base(message)
        {
        }

        public DuplicateEventIdException(string message, Exception innerException) 
            : base(message, innerException)
        {
        }
    }
}
