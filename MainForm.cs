using System;
using System.Drawing;
using System.Windows.Forms;

namespace EventManager
{
    public partial class MainForm : Form
    {
        private EventManager eventManager;
        private MenuStrip menuStrip;
        private ToolStripMenuItem fileMenu, eventMenu, viewMenu, helpMenu;
        private Panel mainPanel;
        private Label titleLabel;
        private Button addWorkshopBtn, addSeminarBtn, viewEventsBtn, exitBtn;
        private TextBox outputTextBox;

        public MainForm()
        {
            InitializeComponent();
            eventManager = new EventManager();
        }

        private void InitializeComponent()
        {
            // Form properties
            this.Text = "Conference Center Event Manager";
            this.Size = new Size(800, 600);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.MinimumSize = new Size(600, 400);

            // Create menu strip
            CreateMenuStrip();

            // Create main panel
            mainPanel = new Panel
            {
                Dock = DockStyle.Fill,
                Padding = new Padding(20)
            };
            this.Controls.Add(mainPanel);

            // Title label
            titleLabel = new Label
            {
                Text = "Conference Center Event Management System",
                Font = new Font("Arial", 16, FontStyle.Bold),
                ForeColor = Color.DarkBlue,
                AutoSize = true,
                Location = new Point(20, 20)
            };
            mainPanel.Controls.Add(titleLabel);

            // Create buttons
            CreateButtons();

            // Create output text box
            CreateOutputTextBox();

            // Set tab order
            SetTabOrder();
        }

        private void CreateMenuStrip()
        {
            menuStrip = new MenuStrip();

            // File menu
            fileMenu = new ToolStripMenuItem("&File");
            fileMenu.DropDownItems.Add("&New Event Manager", null, (s, e) => ClearAllEvents());
            fileMenu.DropDownItems.Add(new ToolStripSeparator());
            fileMenu.DropDownItems.Add("E&xit", null, (s, e) => ExitApplication());

            // Event menu
            eventMenu = new ToolStripMenuItem("&Event");
            eventMenu.DropDownItems.Add("Add &Workshop", null, (s, e) => AddWorkshop());
            eventMenu.DropDownItems.Add("Add &Seminar", null, (s, e) => AddSeminar());
            eventMenu.DropDownItems.Add(new ToolStripSeparator());
            eventMenu.DropDownItems.Add("&View All Events", null, (s, e) => ViewAllEvents());

            // View menu
            viewMenu = new ToolStripMenuItem("&View");
            viewMenu.DropDownItems.Add("&Statistics", null, (s, e) => ShowStatistics());
            viewMenu.DropDownItems.Add("&Clear Output", null, (s, e) => ClearOutput());

            // Help menu
            helpMenu = new ToolStripMenuItem("&Help");
            helpMenu.DropDownItems.Add("&About", null, (s, e) => ShowAbout());

            menuStrip.Items.AddRange(new ToolStripMenuItem[] { fileMenu, eventMenu, viewMenu, helpMenu });
            this.MainMenuStrip = menuStrip;
            this.Controls.Add(menuStrip);
        }

        private void CreateButtons()
        {
            int buttonWidth = 150;
            int buttonHeight = 40;
            int spacing = 20;
            int startY = 80;

            addWorkshopBtn = new Button
            {
                Text = "Add Workshop",
                Size = new Size(buttonWidth, buttonHeight),
                Location = new Point(20, startY),
                BackColor = Color.LightBlue,
                FlatStyle = FlatStyle.Flat
            };
            addWorkshopBtn.Click += (s, e) => AddWorkshop();

            addSeminarBtn = new Button
            {
                Text = "Add Seminar",
                Size = new Size(buttonWidth, buttonHeight),
                Location = new Point(20, startY + buttonHeight + spacing),
                BackColor = Color.LightGreen,
                FlatStyle = FlatStyle.Flat
            };
            addSeminarBtn.Click += (s, e) => AddSeminar();

            viewEventsBtn = new Button
            {
                Text = "View All Events",
                Size = new Size(buttonWidth, buttonHeight),
                Location = new Point(20, startY + 2 * (buttonHeight + spacing)),
                BackColor = Color.LightYellow,
                FlatStyle = FlatStyle.Flat
            };
            viewEventsBtn.Click += (s, e) => ViewAllEvents();

            exitBtn = new Button
            {
                Text = "Exit",
                Size = new Size(buttonWidth, buttonHeight),
                Location = new Point(20, startY + 3 * (buttonHeight + spacing)),
                BackColor = Color.LightCoral,
                FlatStyle = FlatStyle.Flat
            };
            exitBtn.Click += (s, e) => ExitApplication();

            mainPanel.Controls.AddRange(new Control[] { addWorkshopBtn, addSeminarBtn, viewEventsBtn, exitBtn });
        }

        private void CreateOutputTextBox()
        {
            outputTextBox = new TextBox
            {
                Multiline = true,
                ScrollBars = ScrollBars.Vertical,
                ReadOnly = true,
                Font = new Font("Consolas", 10),
                Location = new Point(200, 80),
                Size = new Size(560, 400),
                Anchor = AnchorStyles.Top | AnchorStyles.Bottom | AnchorStyles.Left | AnchorStyles.Right,
                Text = "Welcome to the Conference Center Event Management System!\n\n" +
                       "Use the buttons on the left or the menu bar to manage events.\n\n" +
                       "Features:\n" +
                       "- Add Workshops with topics and companies\n" +
                       "- Add Seminars with speakers\n" +
                       "- View all scheduled events\n" +
                       "- Exception handling for invalid inputs\n\n" +
                       "Click any button to get started!"
            };
            mainPanel.Controls.Add(outputTextBox);
        }

        private void SetTabOrder()
        {
            addWorkshopBtn.TabIndex = 0;
            addSeminarBtn.TabIndex = 1;
            viewEventsBtn.TabIndex = 2;
            exitBtn.TabIndex = 3;
        }

        // Event handling methods using switch statement pattern
        private void AddWorkshop()
        {
            try
            {
                using (var dialog = new AddWorkshopDialog())
                {
                    if (dialog.ShowDialog() == DialogResult.OK)
                    {
                        // Method overloading demonstration
                        if (dialog.UseDefaultCapacity)
                        {
                            eventManager.AddWorkshop(dialog.EventName, dialog.EventID, dialog.Topic, dialog.Company);
                        }
                        else
                        {
                            eventManager.AddWorkshop(dialog.EventName, dialog.EventID, dialog.Capacity, dialog.Topic, dialog.Company);
                        }

                        outputTextBox.AppendText($"\n✓ Workshop '{dialog.EventName}' added successfully!\n");
                        outputTextBox.AppendText($"  Topic: {dialog.Topic}\n");
                        outputTextBox.AppendText($"  Company: {dialog.Company}\n");
                        outputTextBox.AppendText($"  Capacity: {(dialog.UseDefaultCapacity ? "50 (default)" : dialog.Capacity.ToString())}\n\n");
                    }
                }
            }
            catch (Exception ex)
            {
                HandleException(ex, "Error adding workshop");
            }
        }

        private void AddSeminar()
        {
            try
            {
                using (var dialog = new AddSeminarDialog())
                {
                    if (dialog.ShowDialog() == DialogResult.OK)
                    {
                        // Method overloading demonstration
                        if (dialog.UseDefaultCapacity)
                        {
                            eventManager.AddSeminar(dialog.EventName, dialog.EventID, dialog.Speaker);
                        }
                        else
                        {
                            eventManager.AddSeminar(dialog.EventName, dialog.EventID, dialog.Capacity, dialog.Speaker);
                        }

                        outputTextBox.AppendText($"\n✓ Seminar '{dialog.EventName}' added successfully!\n");
                        outputTextBox.AppendText($"  Speaker: {dialog.Speaker}\n");
                        outputTextBox.AppendText($"  Capacity: {(dialog.UseDefaultCapacity ? "50 (default)" : dialog.Capacity.ToString())}\n\n");
                    }
                }
            }
            catch (Exception ex)
            {
                HandleException(ex, "Error adding seminar");
            }
        }

        private void ViewAllEvents()
        {
            try
            {
                string eventsInfo = eventManager.GetAllEventsInfo();
                outputTextBox.Clear();
                outputTextBox.AppendText("=== ALL SCHEDULED EVENTS ===\n\n");
                outputTextBox.AppendText(eventsInfo);
            }
            catch (Exception ex)
            {
                HandleException(ex, "Error viewing events");
            }
        }

        private void ShowStatistics()
        {
            try
            {
                int totalEvents = eventManager.GetEventCount();
                int workshops = eventManager.GetWorkshopCount();
                int seminars = eventManager.GetSeminarCount();

                string stats = $"=== EVENT STATISTICS ===\n\n";
                stats += $"Total Events: {totalEvents}\n";
                stats += $"Workshops: {workshops}\n";
                stats += $"Seminars: {seminars}\n\n";

                outputTextBox.Clear();
                outputTextBox.AppendText(stats);
            }
            catch (Exception ex)
            {
                HandleException(ex, "Error showing statistics");
            }
        }

        private void ClearAllEvents()
        {
            try
            {
                var result = MessageBox.Show("Are you sure you want to clear all events?",
                    "Confirm Clear", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result == DialogResult.Yes)
                {
                    eventManager.ClearAllEvents();
                    outputTextBox.Clear();
                    outputTextBox.AppendText("All events have been cleared.\n\n");
                }
            }
            catch (Exception ex)
            {
                HandleException(ex, "Error clearing events");
            }
        }

        private void ClearOutput()
        {
            outputTextBox.Clear();
            outputTextBox.AppendText("Output cleared.\n\n");
        }

        private void ShowAbout()
        {
            MessageBox.Show(
                "Conference Center Event Management System\n\n" +
                "Features:\n" +
                "• Object-Oriented Programming with Inheritance\n" +
                "• Polymorphism and Method Overloading\n" +
                "• Exception Handling\n" +
                "• Windows Forms GUI\n" +
                "• Menu-driven Interface\n\n" +
                "Developed using C# and .NET Framework",
                "About Event Manager",
                MessageBoxButtons.OK,
                MessageBoxIcon.Information);
        }

        private void ExitApplication()
        {
            var result = MessageBox.Show("Are you sure you want to exit?",
                "Confirm Exit", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

            if (result == DialogResult.Yes)
            {
                Application.Exit();
            }
        }

        // Exception handling with switch statement
        private void HandleException(Exception ex, string context)
        {
            string errorMessage = $"\n❌ {context}:\n";

            switch (ex)
            {
                case InvalidCapacityException ice:
                    errorMessage += $"Invalid Capacity: {ice.Message}";
                    break;
                case DuplicateEventIdException die:
                    errorMessage += $"Duplicate Event ID: {die.Message}";
                    break;
                case ArgumentNullException ane:
                    errorMessage += $"Missing Information: {ane.ParamName} cannot be null or empty.";
                    break;
                case ArgumentException ae:
                    errorMessage += $"Invalid Input: {ae.Message}";
                    break;
                case FormatException fe:
                    errorMessage += $"Format Error: {fe.Message}";
                    break;
                default:
                    errorMessage += $"Unexpected Error: {ex.Message}";
                    break;
            }

            errorMessage += "\n\n";
            outputTextBox.AppendText(errorMessage);

            // Also show a message box for critical errors
            MessageBox.Show(errorMessage, "Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }
}
