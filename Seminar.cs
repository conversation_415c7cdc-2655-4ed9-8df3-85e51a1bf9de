using System;

namespace EventManager
{
    /// <summary>
    /// Seminar class derived from Event
    /// </summary>
    public class Seminar : Event
    {
        private string speaker;

        // Constructor with all parameters
        public Seminar(string eventName, int eventID, int capacity, string speaker)
            : base(eventName, eventID, capacity)
        {
            this.speaker = speaker ?? throw new ArgumentNullException(nameof(speaker));
        }

        // Constructor with default capacity (method overloading)
        public Seminar(string eventName, int eventID, string speaker)
            : base(eventName, eventID)
        {
            this.speaker = speaker ?? throw new ArgumentNullException(nameof(speaker));
        }

        // Properties
        public string Speaker
        {
            get { return speaker; }
            set { speaker = value ?? throw new ArgumentNullException(nameof(value)); }
        }

        // Override abstract method
        public override string GetEventDetails()
        {
            return $"{DisplayInfo()}\nSpeaker: {speaker}";
        }

        // Override virtual method
        public override string GetEventType()
        {
            return "Seminar";
        }

        // Method overloading for displaying seminar-specific information
        public string DisplaySeminarInfo()
        {
            return $"Seminar: {eventName}\nSpeaker: {speaker}";
        }

        public string DisplaySeminarInfo(bool includeCapacity)
        {
            string info = DisplaySeminarInfo();
            if (includeCapacity)
                info += $"\nCapacity: {capacity}";
            return info;
        }

        // Override ToString for easy display
        public override string ToString()
        {
            return $"Seminar - {eventName} (ID: {eventID})";
        }
    }
}
