using System;
using System.Drawing;
using System.Windows.Forms;

namespace EventManager
{
    public partial class AddWorkshopDialog : Form
    {
        private Label lblEventName, lblEventID, lblCapacity, lblTopic, lblCompany;
        private TextBox txtEventName, txtEventID, txtCapacity, txtTopic, txtCompany;
        private CheckBox chkUseDefaultCapacity;
        private Button btnOK, btnCancel;

        // Properties to get the entered values
        public string EventName => txtEventName.Text.Trim();
        public int EventID => int.Parse(txtEventID.Text.Trim());
        public int Capacity => chkUseDefaultCapacity.Checked ? 50 : int.Parse(txtCapacity.Text.Trim());
        public string Topic => txtTopic.Text.Trim();
        public string Company => txtCompany.Text.Trim();
        public bool UseDefaultCapacity => chkUseDefaultCapacity.Checked;

        public AddWorkshopDialog()
        {
            InitializeComponent();
        }

        private void InitializeComponent()
        {
            // Form properties
            this.Text = "Add Workshop";
            this.Size = new Size(400, 350);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;

            // Event Name
            lblEventName = new Label
            {
                Text = "Event Name:",
                Location = new Point(20, 20),
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleLeft
            };
            txtEventName = new TextBox
            {
                Location = new Point(130, 20),
                Size = new Size(220, 23)
            };

            // Event ID
            lblEventID = new Label
            {
                Text = "Event ID:",
                Location = new Point(20, 55),
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleLeft
            };
            txtEventID = new TextBox
            {
                Location = new Point(130, 55),
                Size = new Size(220, 23)
            };

            // Capacity
            lblCapacity = new Label
            {
                Text = "Capacity:",
                Location = new Point(20, 90),
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleLeft
            };
            txtCapacity = new TextBox
            {
                Location = new Point(130, 90),
                Size = new Size(220, 23)
            };

            // Use Default Capacity checkbox
            chkUseDefaultCapacity = new CheckBox
            {
                Text = "Use Default Capacity (50)",
                Location = new Point(130, 120),
                Size = new Size(200, 23),
                Checked = true
            };
            chkUseDefaultCapacity.CheckedChanged += ChkUseDefaultCapacity_CheckedChanged;

            // Topic
            lblTopic = new Label
            {
                Text = "Topic:",
                Location = new Point(20, 155),
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleLeft
            };
            txtTopic = new TextBox
            {
                Location = new Point(130, 155),
                Size = new Size(220, 23)
            };

            // Company
            lblCompany = new Label
            {
                Text = "Company:",
                Location = new Point(20, 190),
                Size = new Size(100, 23),
                TextAlign = ContentAlignment.MiddleLeft
            };
            txtCompany = new TextBox
            {
                Location = new Point(130, 190),
                Size = new Size(220, 23)
            };

            // Buttons
            btnOK = new Button
            {
                Text = "OK",
                Location = new Point(195, 250),
                Size = new Size(75, 30),
                DialogResult = DialogResult.OK
            };
            btnOK.Click += BtnOK_Click;

            btnCancel = new Button
            {
                Text = "Cancel",
                Location = new Point(275, 250),
                Size = new Size(75, 30),
                DialogResult = DialogResult.Cancel
            };

            // Add controls to form
            this.Controls.AddRange(new Control[]
            {
                lblEventName, txtEventName,
                lblEventID, txtEventID,
                lblCapacity, txtCapacity,
                chkUseDefaultCapacity,
                lblTopic, txtTopic,
                lblCompany, txtCompany,
                btnOK, btnCancel
            });

            // Set initial state
            UpdateCapacityControls();

            // Set tab order
            txtEventName.TabIndex = 0;
            txtEventID.TabIndex = 1;
            txtCapacity.TabIndex = 2;
            chkUseDefaultCapacity.TabIndex = 3;
            txtTopic.TabIndex = 4;
            txtCompany.TabIndex = 5;
            btnOK.TabIndex = 6;
            btnCancel.TabIndex = 7;

            // Set accept and cancel buttons
            this.AcceptButton = btnOK;
            this.CancelButton = btnCancel;
        }

        private void ChkUseDefaultCapacity_CheckedChanged(object sender, EventArgs e)
        {
            UpdateCapacityControls();
        }

        private void UpdateCapacityControls()
        {
            txtCapacity.Enabled = !chkUseDefaultCapacity.Checked;
            if (chkUseDefaultCapacity.Checked)
            {
                txtCapacity.Text = "50";
                txtCapacity.BackColor = SystemColors.Control;
            }
            else
            {
                txtCapacity.BackColor = SystemColors.Window;
                if (txtCapacity.Text == "50")
                    txtCapacity.Text = "";
            }
        }

        private void BtnOK_Click(object sender, EventArgs e)
        {
            try
            {
                // Validate inputs
                if (string.IsNullOrWhiteSpace(txtEventName.Text))
                {
                    MessageBox.Show("Please enter an event name.", "Validation Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtEventName.Focus();
                    return;
                }

                if (!int.TryParse(txtEventID.Text.Trim(), out int eventId) || eventId <= 0)
                {
                    MessageBox.Show("Please enter a valid positive Event ID.", "Validation Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtEventID.Focus();
                    return;
                }

                if (!chkUseDefaultCapacity.Checked)
                {
                    if (!int.TryParse(txtCapacity.Text.Trim(), out int capacity) || capacity < 0)
                    {
                        MessageBox.Show("Please enter a valid non-negative capacity.", "Validation Error", 
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        txtCapacity.Focus();
                        return;
                    }
                }

                if (string.IsNullOrWhiteSpace(txtTopic.Text))
                {
                    MessageBox.Show("Please enter a topic.", "Validation Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtTopic.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtCompany.Text))
                {
                    MessageBox.Show("Please enter a company name.", "Validation Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtCompany.Focus();
                    return;
                }

                // If all validations pass, close the dialog
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
